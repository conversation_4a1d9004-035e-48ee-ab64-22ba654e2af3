// Test script to verify structured DISC processing
const fetch = require('node-fetch');

async function testStructuredDisc() {
    console.log('Testing structured DISC processing...');
    
    // Sample DISC responses for testing
    const testResponses = [
        {
            question: "When facing a challenge, I prefer to:",
            selectedAnswer: "Take charge and find a solution quickly",
            discTrait: "D"
        },
        {
            question: "In team meetings, I usually:",
            selectedAnswer: "Share ideas enthusiastically and engage others",
            discTrait: "I"
        },
        {
            question: "When working on projects, I:",
            selectedAnswer: "Focus on maintaining team harmony",
            discTrait: "S"
        },
        {
            question: "My approach to deadlines is:",
            selectedAnswer: "Plan carefully and follow procedures",
            discTrait: "C"
        },
        {
            question: "When making decisions, I:",
            selectedAnswer: "Consider the impact on team relationships",
            discTrait: "S"
        },
        {
            question: "In stressful situations, I:",
            selectedAnswer: "Stay calm and provide support to others",
            discTrait: "S"
        }
    ];

    try {
        const response = await fetch('http://localhost:3000/api/process-disc-assessment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                userEmail: '<EMAIL>',
                userCompany: 'Test Company',
                role: 'Test Role',
                discResponses: testResponses
            })
        });

        const result = await response.json();
        
        if (result.success) {
            console.log('✅ DISC processing successful!');
            console.log('Primary Type:', result.discProfile.primaryType);
            console.log('Scores:', result.discProfile.scores);
            console.log('Has Structured Analysis:', !!result.discProfile.detailedAnalysis);
            console.log('Has Text Report:', !!result.discProfile.detailedReport);
            
            if (result.discProfile.detailedAnalysis) {
                console.log('\n📊 Structured Analysis Sections:');
                Object.keys(result.discProfile.detailedAnalysis).forEach(key => {
                    console.log(`- ${key}: ${typeof result.discProfile.detailedAnalysis[key]}`);
                });
                
                console.log('\n📝 Sample Content:');
                console.log('Overview:', result.discProfile.detailedAnalysis.overview?.substring(0, 100) + '...');
                console.log('Strengths Count:', result.discProfile.detailedAnalysis.strengths?.length);
                console.log('Challenges Count:', result.discProfile.detailedAnalysis.challenges?.length);
            }
        } else {
            console.error('❌ DISC processing failed:', result.error);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testStructuredDisc();
