<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DISC Assessment Test</title>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    <script src="skills-gap-modal.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>DISC Assessment Test Page</h1>
    
    <div class="test-section">
        <h2>1. Test DISC Question Generation</h2>
        <button class="button" onclick="testDiscQuestionGeneration()">Generate DISC Questions</button>
        <div id="question-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test DISC Response Processing</h2>
        <button class="button" onclick="testDiscProcessing()">Process Sample DISC Responses</button>
        <div id="processing-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Database Query</h2>
        <input type="text" id="test-email" placeholder="Enter email to test" value="<EMAIL>">
        <input type="text" id="test-company" placeholder="Enter company" value="Barefoot eLearning">
        <button class="button" onclick="testDatabaseQuery()">Query DISC Profile</button>
        <button class="button" onclick="testSpecificUser()">Test Harry's Profile</button>
        <div id="database-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Test DISC Details Formatting</h2>
        <button class="button" onclick="testDiscFormatting()">Test Report Formatting</button>
        <div id="formatting-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. Test Skills Gap Modal with DISC</h2>
        <button class="button" onclick="testSkillsGapModal()">Open Skills Gap Modal</button>
        <div id="modal-test-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>6. View Raw DISC Report Content</h2>
        <button class="button" onclick="viewRawDiscReport()">View Raw DISC Report</button>
        <div id="raw-report-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>7. Test DISC Parsing Logic</h2>
        <button class="button" onclick="testDiscParsing()">Test Parsing Logic</button>
        <div id="parsing-test-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>8. Test Sample DISC Format</h2>
        <button class="button" onclick="testSampleFormat()">Test Sample Format</button>
        <div id="sample-format-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. Debug Information</h2>
        <button class="button" onclick="showDebugInfo()">Show Debug Info</button>
        <div id="debug-result" class="result"></div>
    </div>

    <script>
        // Initialize Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };
        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        async function testDiscQuestionGeneration() {
            const resultDiv = document.getElementById('question-result');
            resultDiv.innerHTML = 'Testing DISC question generation...';
            
            try {
                const response = await fetch('/api/generate-disc-questions', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        role: 'Manager',
                        section: 'Intermediate',
                        framework: 'Test Framework',
                        email: '<EMAIL>',
                        batchSize: 2
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Success!</strong><br>
                        Generated ${data.length} questions<br>
                        Sample question: ${data[0]?.question?.substring(0, 100)}...<br>
                        DISC traits: ${JSON.stringify(data[0]?.discTraits)}
                    `;
                } else {
                    const error = await response.json();
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `Error: ${error.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        }

        async function testDiscProcessing() {
            const resultDiv = document.getElementById('processing-result');
            resultDiv.innerHTML = 'Testing DISC processing...';
            
            const sampleResponses = [
                { questionId: 1, question: "Test question 1", selectedOption: "Option A", discTrait: "D", scenario: "leadership" },
                { questionId: 2, question: "Test question 2", selectedOption: "Option B", discTrait: "I", scenario: "communication" },
                { questionId: 3, question: "Test question 3", selectedOption: "Option C", discTrait: "S", scenario: "teamwork" },
                { questionId: 4, question: "Test question 4", selectedOption: "Option D", discTrait: "C", scenario: "analysis" },
                { questionId: 5, question: "Test question 5", selectedOption: "Option A", discTrait: "D", scenario: "decision" }
            ];

            try {
                const response = await fetch('/api/process-disc-assessment', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userEmail: '<EMAIL>',
                        userCompany: 'Birmingham',
                        discResponses: sampleResponses,
                        role: 'Manager'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Success!</strong><br>
                        Primary Type: ${data.discProfile?.primaryType}<br>
                        Scores: ${JSON.stringify(data.discProfile?.scores)}<br>
                        Status: ${data.discProfile?.status}
                    `;
                } else {
                    const error = await response.json();
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `Error: ${error.error}<br>Details: ${JSON.stringify(error.details)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        }

        async function testDatabaseQuery() {
            const resultDiv = document.getElementById('database-result');
            const email = document.getElementById('test-email').value;
            const company = document.getElementById('test-company').value;

            resultDiv.innerHTML = 'Querying database...';

            try {
                const userRef = db.collection('companies').doc(company).collection('users').doc(email);
                const userDoc = await userRef.get();

                if (userDoc.exists) {
                    const userData = userDoc.data();
                    const discProfile = userData.discProfile;

                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>User found!</strong><br>
                        DISC Profile: ${discProfile ? 'Present' : 'Not found'}<br>
                        ${discProfile ? `
                            Primary Type: ${discProfile.primaryType}<br>
                            Status: ${discProfile.status}<br>
                            Scores: ${JSON.stringify(discProfile.scores)}<br>
                            Report Length: ${discProfile.detailedReport?.length || 0} chars
                        ` : 'No DISC profile data'}
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = 'User document not found';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        }

        async function testSpecificUser() {
            const resultDiv = document.getElementById('database-result');
            resultDiv.innerHTML = 'Testing Harry\'s DISC profile...';

            try {
                // Test the exact user from the logs
                const userRef = db.collection('companies').doc('Barefoot eLearning').collection('users').doc('<EMAIL>');
                const userDoc = await userRef.get();

                if (userDoc.exists) {
                    const userData = userDoc.data();
                    const discProfile = userData.discProfile;

                    const hasStructured = discProfile && !!discProfile.detailedAnalysis;
                    const hasText = discProfile && !!discProfile.detailedReport;

                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Harry's Profile Found!</strong><br>
                        Company: Barefoot eLearning<br>
                        Email: <EMAIL><br>
                        DISC Profile: ${discProfile ? 'Present' : 'Not found'}<br>
                        ${discProfile ? `
                            Primary Type: ${discProfile.primaryType}<br>
                            Status: ${discProfile.status}<br>
                            Scores: D:${discProfile.scores?.D}%, I:${discProfile.scores?.I}%, S:${discProfile.scores?.S}%, C:${discProfile.scores?.C}%<br>
                            Timestamp: ${discProfile.timestamp ? new Date(discProfile.timestamp.seconds * 1000).toLocaleString() : 'N/A'}<br>
                            Format: ${hasStructured ? 'Structured JSON' : hasText ? 'Legacy Text' : 'None'}<br>
                            ${hasStructured ? `Structured Sections: ${Object.keys(discProfile.detailedAnalysis).join(', ')}<br>` : ''}
                            ${hasText ? `Text Report: ${discProfile.detailedReport.length} characters<br>` : ''}
                            <button onclick="testModalIntegration()">Test Skills Gap Modal</button>
                        ` : 'No DISC profile data found'}
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = 'Harry\'s user document not found in Barefoot eLearning company';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        }

        async function testDiscFormatting() {
            const resultDiv = document.getElementById('formatting-result');
            resultDiv.innerHTML = 'Testing DISC report formatting...';

            try {
                // Get Harry's actual DISC profile to test formatting
                const userRef = db.collection('companies').doc('Barefoot eLearning').collection('users').doc('<EMAIL>');
                const userDoc = await userRef.get();

                if (userDoc.exists) {
                    const userData = userDoc.data();
                    const discProfile = userData.discProfile;

                    if (discProfile && discProfile.detailedReport) {
                        // Create a test container to show formatted report
                        const testContainer = document.createElement('div');
                        testContainer.style.maxHeight = '400px';
                        testContainer.style.overflow = 'auto';
                        testContainer.style.border = '1px solid #ccc';
                        testContainer.style.padding = '15px';
                        testContainer.style.marginTop = '10px';
                        testContainer.style.backgroundColor = '#f9f9f9';

                        // Test the formatting function (we'll need to copy it here for testing)
                        const formattedReport = formatDiscReportTest(discProfile.detailedReport);
                        testContainer.innerHTML = formattedReport;

                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <strong>DISC Report Formatting Test:</strong><br>
                            Original length: ${discProfile.detailedReport.length} characters<br>
                            Primary Type: ${discProfile.primaryType}<br>
                            <strong>Formatted Preview:</strong>
                        `;
                        resultDiv.appendChild(testContainer);
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = 'No detailed report found in DISC profile';
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = 'User not found for formatting test';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        }

        // Improved formatting function for testing
        function formatDiscReportTest(detailedReport) {
            if (!detailedReport) return '<div style="text-align: center; padding: 1rem; color: #64748b;">No detailed report available</div>';

            const sectionIcons = {
                'OVERVIEW': '👤', 'STRENGTHS': '💪', 'POTENTIAL CHALLENGES': '⚠️', 'CHALLENGES': '⚠️',
                'COMMUNICATION STYLE': '💬', 'COMMUNICATION': '💬', 'WORK ENVIRONMENT': '🏢', 'WORK': '🏢',
                'LEADERSHIP STYLE': '👑', 'LEADERSHIP': '👑', 'TEAM DYNAMICS': '🤝', 'TEAM': '🤝',
                'PROFESSIONAL DEVELOPMENT': '📈', 'DEVELOPMENT': '📈'
            };

            let formattedReport = `
                <div style="margin-bottom: 1.5rem; padding: 1rem; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 6px; border: 1px solid #e2e8f0; text-align: center;">
                    <h5 style="margin: 0 0 0.5rem 0; color: #1e293b; font-size: 1rem; font-weight: 600;">Detailed Behavioral Analysis</h5>
                    <p style="margin: 0; color: #64748b; font-size: 0.875rem;">Comprehensive insights into your personality profile</p>
                </div>
            `;

            // Parse the report content to extract sections
            const sectionPattern = /──+\s*(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT)\s*(?:\n|$)/gi;

            // Split by section headers
            const sections = detailedReport.split(sectionPattern);

            // If no clear sections found, try alternative parsing
            if (sections.length <= 2) {
                return parseAlternativeFormatTest(detailedReport, sectionIcons, formattedReport);
            }

            // Process sections found by the pattern
            for (let i = 1; i < sections.length; i += 2) {
                const sectionTitle = sections[i];
                const sectionContent = sections[i + 1];

                if (sectionTitle && sectionContent) {
                    const cleanTitle = sectionTitle.trim().toUpperCase();
                    const displayTitle = cleanTitle.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                    const icon = sectionIcons[cleanTitle] || sectionIcons[Object.keys(sectionIcons).find(key => cleanTitle.includes(key))] || '📋';

                    formattedReport += `
                        <div style="margin-bottom: 1.5rem; padding-bottom: 1.5rem; border-bottom: 1px solid #f1f5f9;">
                            <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 0.75rem;">
                                <div style="width: 20px; height: 20px; border-radius: 4px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 0.7rem;">${icon}</div>
                                <h4 style="margin: 0; color: #1e293b; font-size: 1rem; font-weight: 600; letter-spacing: 0.025em;">${displayTitle}</h4>
                            </div>
                            <div style="color: #475569; line-height: 1.6; font-size: 0.9rem; margin-left: 2.25rem;">${formatContentTest(sectionContent.trim())}</div>
                        </div>
                    `;
                }
            }

            return formattedReport;
        }

        function parseAlternativeFormatTest(detailedReport, sectionIcons, formattedReport) {
            console.log('Using alternative parsing format for test');

            // Look for section headers that appear as standalone lines or with decorative elements
            const lines = detailedReport.split('\n');
            let currentSection = null;
            let currentContent = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();

                // Check if this line contains a section header
                const sectionMatch = line.match(/(?:──+\s*)?(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT)(?:\s*──+)?/i);

                if (sectionMatch) {
                    // Save previous section if exists
                    if (currentSection && currentContent.length > 0) {
                        const cleanTitle = currentSection.toUpperCase();
                        const displayTitle = cleanTitle.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                        const icon = sectionIcons[cleanTitle] || sectionIcons[Object.keys(sectionIcons).find(key => cleanTitle.includes(key))] || '📋';

                        formattedReport += `
                            <div style="margin-bottom: 1.5rem; padding-bottom: 1.5rem; border-bottom: 1px solid #f1f5f9;">
                                <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 0.75rem;">
                                    <div style="width: 20px; height: 20px; border-radius: 4px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 0.7rem;">${icon}</div>
                                    <h4 style="margin: 0; color: #1e293b; font-size: 1rem; font-weight: 600; letter-spacing: 0.025em;">${displayTitle}</h4>
                                </div>
                                <div style="color: #475569; line-height: 1.6; font-size: 0.9rem; margin-left: 2.25rem;">${formatContentTest(currentContent.join('\n').trim())}</div>
                            </div>
                        `;
                    }

                    // Start new section
                    currentSection = sectionMatch[1];
                    currentContent = [];
                } else if (line.length > 0 && currentSection) {
                    // Add content to current section
                    currentContent.push(line);
                } else if (line.length > 0 && !currentSection) {
                    // Content before any section header - treat as overview or general content
                    if (!currentSection) {
                        currentSection = 'OVERVIEW';
                        currentContent = [line];
                    }
                }
            }

            // Save the last section
            if (currentSection && currentContent.length > 0) {
                const cleanTitle = currentSection.toUpperCase();
                const displayTitle = cleanTitle.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                const icon = sectionIcons[cleanTitle] || sectionIcons[Object.keys(sectionIcons).find(key => cleanTitle.includes(key))] || '📋';

                formattedReport += `
                    <div style="margin-bottom: 1.5rem; padding-bottom: 1.5rem; border-bottom: 1px solid #f1f5f9;">
                        <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 0.75rem;">
                            <div style="width: 20px; height: 20px; border-radius: 4px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 0.7rem;">${icon}</div>
                            <h4 style="margin: 0; color: #1e293b; font-size: 1rem; font-weight: 600; letter-spacing: 0.025em;">${displayTitle}</h4>
                        </div>
                        <div style="color: #475569; line-height: 1.6; font-size: 0.9rem; margin-left: 2.25rem;">${formatContentTest(currentContent.join('\n').trim())}</div>
                    </div>
                `;
            }

            return formattedReport;
        }

        function formatContentTest(content) {
            const paragraphs = content.split(/\n\s*\n/);
            let result = '';

            paragraphs.forEach(paragraph => {
                const trimmed = paragraph.trim();
                if (!trimmed) return;

                const lines = trimmed.split('\n');
                const hasBullets = lines.some(line => line.trim().match(/^[-•*]\s+/));

                if (hasBullets) {
                    result += '<ul style="margin: 0.5rem 0; padding-left: 0; list-style: none;">';
                    lines.forEach(line => {
                        const trimmedLine = line.trim();
                        if (trimmedLine.match(/^[-•*]\s+/)) {
                            const listItem = trimmedLine.replace(/^[-•*]\s+/, '');
                            result += `<li style="position: relative; padding-left: 1.25rem; margin-bottom: 0.4rem; line-height: 1.5;"><span style="position: absolute; left: 0; color: #3b82f6; font-weight: 600;">•</span>${listItem}</li>`;
                        }
                    });
                    result += '</ul>';
                } else {
                    result += `<p style="margin: 0 0 0.75rem 0; line-height: 1.6;">${trimmed}</p>`;
                }
            });

            return result;
        }

        function showDebugInfo() {
            const resultDiv = document.getElementById('debug-result');
            resultDiv.className = 'result';
            resultDiv.innerHTML = `
                <strong>Debug Information:</strong><br>
                Current URL: ${window.location.href}<br>
                Firebase initialized: ${firebase.apps.length > 0}<br>
                Firestore available: ${typeof db !== 'undefined'}<br>
                Window.discResponses: ${window.discResponses ? window.discResponses.length + ' responses' : 'Not initialized'}<br>
                Toggle function available: ${typeof window.toggleDiscDetails !== 'undefined'}<br>
                Local storage keys: ${Object.keys(localStorage).join(', ')}<br>
                Session storage keys: ${Object.keys(sessionStorage).join(', ')}
            `;
        }

        // Test function to open skills gap modal with DISC data
        async function testSkillsGapModal() {
            const resultDiv = document.getElementById('modal-test-result');
            resultDiv.innerHTML = 'Opening Skills Gap Modal with DISC data...';

            try {
                // Create mock assessment data that includes DISC profile info
                const mockData = {
                    report: {
                        competencyAnalysis: "Mock competency analysis for testing",
                        summary: "Mock summary for testing DISC integration",
                        employeeEmail: "<EMAIL>",
                        userCompany: "Barefoot eLearning"
                    },
                    recommendations: [
                        { course: "Test Course 1", reason: "Test reason 1" },
                        { course: "Test Course 2", reason: "Test reason 2" }
                    ]
                };

                // Call the skills gap modal function
                if (typeof showSkillsGapAnalysis === 'function') {
                    await showSkillsGapAnalysis(mockData);
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = 'Skills Gap Modal opened successfully! Check the modal for DISC profile display and browser console for debugging output.';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = 'Error: showSkillsGapAnalysis function not found. Make sure skills-gap-modal.js is loaded.';
                }
            } catch (error) {
                console.error('Error opening skills gap modal:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Error opening modal: ${error.message}`;
            }
        }

        // Function to view raw DISC report content
        async function viewRawDiscReport() {
            const resultDiv = document.getElementById('raw-report-result');
            resultDiv.innerHTML = 'Fetching raw DISC report...';

            try {
                const userRef = db.collection('companies').doc('Barefoot eLearning').collection('users').doc('<EMAIL>');
                const userDoc = await userRef.get();

                if (userDoc.exists) {
                    const userData = userDoc.data();
                    const discProfile = userData.discProfile;

                    if (discProfile && discProfile.detailedReport) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <strong>Raw DISC Report Content:</strong><br>
                            <strong>Primary Type:</strong> ${discProfile.primaryType}<br>
                            <strong>Report Length:</strong> ${discProfile.detailedReport.length} characters<br>
                            <strong>Status:</strong> ${discProfile.status}<br><br>
                            <strong>Raw Content (first 1000 chars):</strong><br>
                            <pre style="background: #f5f5f5; padding: 10px; white-space: pre-wrap; font-size: 12px; max-height: 300px; overflow-y: auto;">${discProfile.detailedReport.substring(0, 1000)}...</pre>
                            <br>
                            <strong>Full Content:</strong><br>
                            <pre style="background: #f5f5f5; padding: 10px; white-space: pre-wrap; font-size: 12px; max-height: 400px; overflow-y: auto;">${discProfile.detailedReport}</pre>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = 'No detailed DISC report found';
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = 'User document not found';
                }
            } catch (error) {
                console.error('Error fetching raw DISC report:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        }

        // Function to test DISC parsing logic
        async function testDiscParsing() {
            const resultDiv = document.getElementById('parsing-test-result');
            resultDiv.innerHTML = 'Testing DISC parsing logic...';

            try {
                const userRef = db.collection('companies').doc('Barefoot eLearning').collection('users').doc('<EMAIL>');
                const userDoc = await userRef.get();

                if (userDoc.exists) {
                    const userData = userDoc.data();
                    const discProfile = userData.discProfile;

                    if (discProfile && discProfile.detailedReport) {
                        const detailedReport = discProfile.detailedReport;

                        console.log('=== DISC PARSING TEST ===');
                        console.log('Report length:', detailedReport.length);
                        console.log('Full report content:', detailedReport);

                        // Test the primary pattern
                        const sectionPattern = /[-─═_]{3,}\s*(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT)\s*[-─═_]*\s*(?:\n|$)/gi;
                        const sections = detailedReport.split(sectionPattern);

                        console.log('Primary pattern sections found:', sections.length);
                        console.log('Sections array:', sections);

                        // Test alternative pattern
                        const lines = detailedReport.split('\n');
                        console.log('Total lines:', lines.length);

                        let sectionHeaders = [];
                        lines.forEach((line, index) => {
                            const sectionMatch = line.match(/(?:[-─═_]{3,}\s*)?(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT)(?:\s*[-─═_]*)?/i);
                            if (sectionMatch) {
                                sectionHeaders.push({
                                    line: index + 1,
                                    content: line.trim(),
                                    match: sectionMatch[1]
                                });
                            }
                        });

                        console.log('Section headers found:', sectionHeaders);

                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <strong>DISC Parsing Test Results:</strong><br>
                            <strong>Report Length:</strong> ${detailedReport.length} characters<br>
                            <strong>Primary Pattern Sections:</strong> ${sections.length}<br>
                            <strong>Section Headers Found:</strong> ${sectionHeaders.length}<br><br>

                            <strong>Section Headers:</strong><br>
                            ${sectionHeaders.map(h => `Line ${h.line}: "${h.content}" → ${h.match}`).join('<br>')}<br><br>

                            <strong>First 500 characters:</strong><br>
                            <pre style="background: #f5f5f5; padding: 10px; white-space: pre-wrap; font-size: 12px;">${detailedReport.substring(0, 500)}</pre>

                            <strong>Check browser console for full details</strong>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = 'No detailed DISC report found';
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = 'User document not found';
                }
            } catch (error) {
                console.error('Error testing DISC parsing:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        }

        // Function to test sample DISC format
        function testSampleFormat() {
            const resultDiv = document.getElementById('sample-format-result');
            resultDiv.innerHTML = 'Testing sample DISC format...';

            // Sample DISC report in the format you provided
            const sampleReport = `Detailed Behavioral Analysis
Comprehensive insights into your Steadiness personality profile

──────────────────────────── OVERVIEW
This individual's primary behavioral style is Steadiness (S), complemented by an influential (I) tendency. With a lower Dominance (D) and minimal emphasis on Conscientiousness (C), the profile reflects someone who values collaboration, consistency, and supportive relationships. Their calm, considerate, and empathetic nature makes them a reliable presence in caregiving roles.

──────────────────────────── STRENGTHS
Consistency and Reliability: A strong Steadiness component ensures they are dependable, conscientious about following routines, and consistently provide support to those in their care.
Empathy and Approachability: An influential style enhances their interpersonal communication, making them warm and understanding when interacting with clients and colleagues.
Team Orientation: Their natural inclination towards collaboration helps create a harmonious work atmosphere and encourages mutual support among team members.

──────────────────────────── POTENTIAL CHALLENGES
Adaptability to Change: With a strong preference for stability, sudden changes or unpredictable shifts in routines may cause discomfort.
Assertiveness in Complex Situations: A lower Dominance score may lead to difficulties in asserting themselves during conflicts or when quick decisions are needed.
Attention to Detail: The absence of Conscientiousness suggests that while they excel in interpersonal roles, meticulousness in administrative tasks or documentation might require additional support or training.

──────────────────────────── COMMUNICATION STYLE
They thrive in open, honest, and friendly exchanges. They respond best to clear, calm, and supportive communication that acknowledges their contributions. Feedback delivered in a respectful and constructive manner is especially effective, enabling them to feel valued and understood.

──────────────────────────── WORK ENVIRONMENT
A work setting that is stable, predictable, and team-oriented is ideal. Environments where routines and clear processes are observed will help reduce stress and enhance productivity. Spaces that encourage support and empathetic interactions, with minimal abrupt changes, will allow them to flourish.

──────────────────────────── LEADERSHIP STYLE
When leading, they tend to be supportive rather than directive, preferring to foster collaboration and trust within the team. Conversely, they excel under leaders who provide steady guidance, clear expectations, and supportive feedback, ensuring that they feel secure and valued in their role.

──────────────────────────── TEAM DYNAMICS
They are excellent team players who contribute by building strong, supportive relationships. Their empathetic nature and willingness to assist colleagues make them a valuable asset in team projects, promoting an inclusive and cooperative atmosphere. However, they may sometimes need encouragement to engage in assertive decision-making activities.

──────────────────────────── PROFESSIONAL DEVELOPMENT
Enhance Flexibility: Training on managing change and adapting to unexpected situations can help them build confidence in dynamic settings.
Assertiveness Skills: Workshops or mentorship programs focused on communication strategies may aid in improving self-advocacy during challenging scenarios.
Strengthen Administrative Competencies: Developing skills in basic data management or documentation can balance their natural proficiency in interpersonal care with the technical aspects of their role.

This comprehensive assessment provides actionable insights to leverage inherent strengths while addressing potential challenges, supporting continuous personal and professional growth in a care-focused environment.`;

            console.log('=== SAMPLE FORMAT TEST ===');
            console.log('Sample report length:', sampleReport.length);
            console.log('Sample report content:', sampleReport);

            // Test the parsing logic
            const sectionPattern = /[-─═_]{3,}\s*(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT)\s*[-─═_]*\s*(?:\n|$)/gi;
            const sections = sampleReport.split(sectionPattern);

            console.log('Sample format sections found:', sections.length);
            console.log('Sample sections array:', sections);

            // Test alternative parsing
            const lines = sampleReport.split('\n');
            let sectionHeaders = [];
            lines.forEach((line, index) => {
                let sectionMatch = line.match(/(?:[-─═_]{3,}\s*)?(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT)(?:\s*[-─═_]*)?/i);
                if (!sectionMatch) {
                    sectionMatch = line.match(/^(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT)\s*$/i);
                }
                if (sectionMatch) {
                    sectionHeaders.push({
                        line: index + 1,
                        content: line.trim(),
                        match: sectionMatch[1]
                    });
                }
            });

            console.log('Sample section headers found:', sectionHeaders);

            // Test the actual formatting function if available
            let formattedResult = '';
            if (typeof formatDiscReportTest === 'function') {
                formattedResult = formatDiscReportTest(sampleReport);
                console.log('Formatted result length:', formattedResult.length);
            }

            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <strong>Sample Format Test Results:</strong><br>
                <strong>Sample Report Length:</strong> ${sampleReport.length} characters<br>
                <strong>Primary Pattern Sections:</strong> ${sections.length}<br>
                <strong>Section Headers Found:</strong> ${sectionHeaders.length}<br><br>

                <strong>Section Headers:</strong><br>
                ${sectionHeaders.map(h => `Line ${h.line}: "${h.content}" → ${h.match}`).join('<br>')}<br><br>

                ${formattedResult ? `<strong>Formatted Output:</strong><br>${formattedResult}` : '<strong>Formatting function not available</strong>'}

                <strong>Check browser console for full details</strong>
            `;
        }

        // Function to test modal integration
        function testModalIntegration() {
            const resultDiv = document.getElementById('modal-test-result');
            if (!resultDiv) {
                alert('Modal test result div not found. Please run this from the skills gap modal test section.');
                return;
            }

            resultDiv.innerHTML = 'Testing modal integration...';

            // Simulate opening the skills gap modal with DISC data
            const testData = {
                userEmail: '<EMAIL>',
                userCompany: 'Barefoot eLearning',
                assessmentData: {
                    role: 'Care Assistant',
                    employeeEmail: '<EMAIL>',
                    userCompany: 'Barefoot eLearning'
                }
            };

            console.log('Testing modal integration with data:', testData);

            // This would normally trigger the modal, but for testing we'll just log
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <strong>Modal Integration Test</strong><br>
                User: ${testData.userEmail}<br>
                Company: ${testData.userCompany}<br>
                Role: ${testData.assessmentData.role}<br>
                <br>
                <strong>To test the actual modal:</strong><br>
                1. Go to the main assessment page<br>
                2. Complete an assessment as Harry<br>
                3. Open the skills gap modal<br>
                4. Check the DISC profile section<br>
                <br>
                <strong>Expected Result:</strong><br>
                - DISC badge should show "I" (Influence)<br>
                - Learn More should display structured sections<br>
                - All 8 sections should be visible and properly formatted
            `;
        }
    </script>
</body>
</html>
