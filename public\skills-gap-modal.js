(function(global) {
    // Configuration and state
    let isModalInitialized = false;
    let isClosing = false;
    let currentChartInstance = null;
    let currentData = null;

    async function showSkillsGapAnalysis(data = null) {
        try {
            if (data) {
                currentData = data;  // Store the data
            }

            if (isModalInitialized) {
                await resetAndShowModal();
                return;
            }

            isClosing = false;

            // Create modal structure
            const overlay = document.createElement('div');
            overlay.id = 'skills-gap-overlay';
            overlay.className = 'modal-overlay';

            if (currentData) {
                overlay.innerHTML = createModalHTML(currentData);
            } else {
                throw new Error('Invalid data structure for modal creation');
            }

            document.body.appendChild(overlay);
            isModalInitialized = true;

            // Initialize event listeners
            initializeEventListeners(overlay);

            // Create radar chart with actual data
            await createRadarChart(currentData);

            // Load DISC profile if user data is available
            if (currentData && currentData.report && currentData.report.employeeEmail) {
                const userCompany = currentData.report.userCompany || 'Birmingham'; // Default company
                console.log('Skills gap modal loading DISC profile:', {
                    email: currentData.report.employeeEmail,
                    userCompany: userCompany,
                    hasUserCompanyInData: !!currentData.report.userCompany
                });
                await loadDiscProfile(currentData.report.employeeEmail, userCompany);

                // Start polling for DISC profile updates if it's still processing
                startDiscProfilePolling(currentData.report.employeeEmail, userCompany);
            }

            // Animate modal appearance
            requestAnimationFrame(() => {
                if (!isClosing) {
                    overlay.style.opacity = '1';
                    const modalContent = overlay.querySelector('.modal-content');
                    if (modalContent) {
                        modalContent.style.opacity = '1';
                        modalContent.style.transform = 'scale(1)';
                    }
                }
            });

            addStyles();
        } catch (error) {
            console.error('Error showing skills gap modal:', error);
            throw error;
        }
    }

    async function resetAndShowModal() {
        const overlay = document.getElementById('skills-gap-overlay');
        if (!overlay) return;

        // Clear existing chart container
        const chartContainer = overlay.querySelector('#radar-chart');
        if (chartContainer) {
            chartContainer.innerHTML = '';
        }

        // Recreate chart with updated data
        await createRadarChart(currentData);

        // Show modal
        overlay.style.display = 'flex';
        requestAnimationFrame(() => {
            overlay.style.opacity = '1';
            const modalContent = overlay.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.opacity = '1';
                modalContent.style.transform = 'scale(1)';
            }
        });
    }

    function createCompetencyCard(competency, data) {
        // Check if we have any strength areas
        const hasStrengths = Array.isArray(data.strengthAreas) && data.strengthAreas.length > 0;

        // Check if we have any gap areas
        const hasGaps = Array.isArray(data.gapAreas) && data.gapAreas.length > 0;

        // Create the strength areas HTML if we have any
        const strengthAreasHTML = hasStrengths
            ? `
                <div class="strength-areas">
                    <h4>Strengths</h4>
                    ${data.strengthAreas.map(area => `
                        <span class="badge strength">${area}</span>
                    `).join('')}
                </div>
            `
            : '';

        // Create the gap areas HTML if we have any
        const gapAreasHTML = hasGaps
            ? `
                <div class="gap-areas">
                    <h4>Gaps</h4>
                    ${data.gapAreas.map(area => `
                        <span class="badge gap">${area}</span>
                    `).join('')}
                </div>
            `
            : '';

        // Create the no data message if we don't have any strengths or gaps
        const noDataHTML = !hasStrengths && !hasGaps
            ? `<div class="no-data">No data available</div>`
            : '';

        return `
            <div class="competency-card">
                <h3>${competency}</h3>
                <div class="proficiency-meter">
                    <div class="progress-bar">
                        <div class="progress" style="width: ${data.proficiencyLevel}"></div>
                    </div>
                    <span class="proficiency-level">${data.proficiencyLevel}</span>
                </div>
                <div class="areas-section">
                    ${strengthAreasHTML}
                    ${gapAreasHTML}
                    ${noDataHTML}
                </div>
            </div>
        `;
    }

    async function createRadarChart(data = null) {
        try {
            if (!data || !data.report || !data.report.competencyAnalysis) {
                throw new Error('Invalid data for radar chart creation');
            }

            // Cleanup existing chart instance if any
            if (currentChartInstance) {
                currentChartInstance.destroy();
                currentChartInstance = null;
            }

            const chartData = Object.entries(data.report.competencyAnalysis).map(([name, compData]) => ({
                axis: name,
                value: parseInt(compData.proficiencyLevel) || 0
            }));

            // Load Chart.js if not already present
            if (!window.Chart) {
                await new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js';
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }

            const container = document.querySelector('#radar-chart');
            if (!container) {
                throw new Error('Radar chart container not found');
            }

            // Prepare container
            container.innerHTML = '';
            const ctx = document.createElement('canvas');
            container.appendChild(ctx);

            // Instantiate chart
            currentChartInstance = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: chartData.map(d => d.axis),
                    datasets: [{
                        data: chartData.map(d => d.value),
                        backgroundColor: 'rgba(30, 58, 138, 0.1)',
                        borderColor: '#1e3a8a',
                        pointBackgroundColor: '#1e3a8a'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: { display: false },
                            grid: { color: '#d1d5db' },
                            angleLines: { color: '#d1d5db' }
                        }
                    },
                    plugins: {
                        legend: { display: false }
                    }
                }
            });
        } catch (error) {
            console.error('Error creating radar chart:', error);
            throw error;
        }
    }

    function createModalHTML(data) {
        const competencyCards = data
            ? Object.entries(data.report.competencyAnalysis)
                  .map(([competency, competencyData]) =>
                      createCompetencyCard(competency, competencyData)
                  )
                  .join('')
            : '';

        // Core (primary) recommendations
        const coreRecommendations = data.recommendations.map(rec => `
            <li>
                <strong>${rec.course}</strong>
                <p>${rec.reason}</p>
            </li>
        `).join('');

        // Additional (collapsible) recommendations
        const crossPathSection = data.other_learning_paths_courses && data.other_learning_paths_courses.length > 0
            ? `
                <div class="recommendations-section cross-path-recommendations">
                    <div class="cross-path-header">
                        <h3>Additional Learning Path Recommendations</h3>
                        <button id="toggle-cross-paths-btn" class="toggle-cross-paths-btn">Show/Hide</button>
                    </div>
                    <div id="cross-paths-collapsible" class="cross-paths-collapsible">
                        <ul>
                            ${
                                data.other_learning_paths_courses.map(rec => `
                                    <li>
                                        <strong>${rec.course}</strong>
                                        <p>${rec.reason}</p>
                                    </li>
                                `).join('')
                            }
                        </ul>
                    </div>
                </div>
            `
            : '';

        return `
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title-container">
                        <h2 class="modal-employee-title">
                            ${data && data.report ? `${data.report.employeeName || ''} ${data.report.role ? `- ${data.report.role}` : ''}` : ''}
                        </h2>
                        <h3 class="modal-subtitle">Skills Gap Analysis Report</h3>
                    </div>
                    <div class="modal-actions">
                        <button id="close-skills-modal" class="close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="modal-body">
                    <div id="radar-chart" class="chart-container"></div>

                    <div class="competency-grid">
                        ${competencyCards}
                    </div>

                    <div class="learning-path-section">
                        <h3>Learning Path Recommendations</h3>
                        <p>${data ? data.report.summary : ''}</p>
                    </div>

                    <div class="recommendations-container">
                        <div class="recommendations-section primary-recommendations">
                            <h3>Core Learning Path Recommendations</h3>
                            <ul>
                                ${coreRecommendations}
                            </ul>
                        </div>
                        ${crossPathSection}
                    </div>

                    <div id="disc-profile-section" class="disc-profile-section">
                        <h3>DISC Behavioral Profile</h3>
                        <div id="disc-profile-content" class="disc-profile-content">
                            <div class="disc-loading">
                                <div class="disc-spinner"></div>
                                <p>Processing behavioral assessment...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function addStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            /***** OVERLAY *****/
            .modal-overlay {
                position: fixed;
                top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0, 0, 0, 0.4);
                display: flex;
                justify-content: center;
                align-items: center;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 1000;
            }

            /***** MODAL CONTAINER *****/
            .modal-content {
                background: #ffffff;
                border-radius: 6px;
                width: 90%;
                max-width: 900px;
                max-height: 90vh;
                overflow-y: auto;
                opacity: 0;
                transform: scale(0.95);
                transition: all 0.3s ease;
                font-family: sans-serif;
                color: #374151;
                position: relative;
                /* Slightly smaller base font for a minimal look */
                font-size: 0.875rem;
            }

            /***** HEADER *****/
            .modal-header {
                padding: 1rem;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }

            .modal-title-container {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }

            .modal-employee-title {
                font-size: 1rem; /* reduced from 1.2rem */
                font-weight: 600;
                color: #1e3a8a;
                margin: 0;
            }

            .modal-subtitle {
                font-size: 0.8rem; /* reduced from 0.9rem */
                font-weight: 500;
                color: #4b5563;
                margin: 0;
            }

            /***** ACTIONS *****/
            .modal-actions {
                display: flex;
                gap: 0.5rem;
            }

            .close-modal-button {
                background: none;
                border: 1px solid #1e3a8a;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                cursor: pointer;
                color: #1e3a8a;
            }

            .close-modal-button svg {
                stroke: #1e3a8a;
            }

            .close-modal-button:hover {
                background: #1e3a8a;
                color: #fff;
            }

            /***** BODY *****/
            .modal-body {
                padding: 1rem;
                line-height: 1.4;
            }

            .chart-container {
                position: relative;
                height: 300px;
                width: 80%;  /* center by setting width and margin auto */
                margin: 0 auto 1.5rem;
            }

            /***** COMPETENCY GRID *****/
            .competency-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.75rem;
                margin-bottom: 1.5rem;
                padding: 0 1rem;
            }

            .competency-card {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .competency-card h3 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem; /* reduced from 1rem */
                color: #1e3a8a;
            }

            /***** PROFICIENCY METER *****/
            .proficiency-meter {
                margin: 0.75rem 0;
                display: flex;
                align-items: center;
            }

            .progress-bar {
                background: #e5e7eb;
                border-radius: 9999px;
                height: 6px;
                width: 100%;
                margin-right: 0.5rem;
                overflow: hidden;
            }

            .progress {
                background: #1547bb; /* Changed from #f59e0b (orange) to main blue */
                border-radius: 9999px;
                height: 100%;
                transition: width 0.3s ease;
            }

            .proficiency-level {
                font-size: 0.75rem;
                color: #6b7280;
            }

            /***** STRENGTH & GAP AREAS *****/
            .areas-section h4 {
                margin: 0.5rem 0 0.25rem;
                font-size: 0.8rem;
                font-weight: 600;
                color: #374151;
            }

            .badge {
                display: inline-block;
                padding: 0.25rem 0.5rem;
                border-radius: 9999px;
                font-size: 0.75rem;
                margin: 0.25rem 0.25rem 0 0;
            }

            .badge.strength {
                background: #f0f9ff; /* lighter than #e0f2fe */
                color: #1e3a8a;
            }

            .badge.gap {
                background: #fef3f2; /* lighter red background */
                color: #9b1c1c;
            }

            .no-data {
                font-size: 0.8rem;
                color: #6b7280;
                font-style: italic;
                padding: 0.5rem 0;
                text-align: center;
            }

            /***** LEARNING PATH *****/
            .learning-path-section {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f9fafb;
                margin: 0 1rem 1.5rem;
            }

            .learning-path-section h3 {
                margin-top: 0;
                font-size: 0.9rem; /* reduced from 1rem */
                color: #1e3a8a;
                margin-bottom: 0.5rem;
                font-weight: 600;
            }

            /***** RECOMMENDATIONS *****/
            .recommendations-container {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                padding: 0 1rem;
            }

            .recommendations-section {
                background: #f9fafb;
                border-radius: 6px;
                padding: 1rem;
                border-left: 4px solid transparent;
            }

            .recommendations-section ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .recommendations-section li {
                border-bottom: 1px solid #e5e7eb;
                padding: 0.5rem 0;
            }

            .recommendations-section li:last-child {
                border-bottom: none;
            }

            .primary-recommendations {
                border-left-color: #1e3a8a;
            }

            .cross-path-recommendations {
                border-left-color: #059669;
            }

            .recommendations-section h3 {
                margin-top: 0;
                margin-bottom: 0.75rem;
                font-weight: 600;
                font-size: 0.9rem;
                color: #374151;
            }

            /***** COLLAPSIBLE CROSS-PATH *****/
            .cross-path-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.25rem;
            }

            .toggle-cross-paths-btn {
                background: none;
                border: 1px solid #059669;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                font-size: 0.8rem;
                cursor: pointer;
                color: #059669;
            }

            .toggle-cross-paths-btn:hover {
                background: #059669;
                color: #fff;
            }

            .cross-paths-collapsible {
                max-height: 0;
                opacity: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
            }

            .cross-paths-collapsible.expanded {
                max-height: 1000px; /* Adjust based on expected content height */
                opacity: 1;
            }

            /***** DISC PROFILE SECTION *****/
            .disc-profile-section {
                margin-top: 2rem;
                padding: 1.5rem;
                background: linear-gradient(135deg, #f8fafc, #f1f5f9);
                border-radius: 8px;
                border: 1px solid #e2e8f0;
            }

            .disc-profile-section h3 {
                margin: 0 0 1rem 0;
                color: #1e293b;
                font-size: 1.25rem;
                font-weight: 600;
            }

            .disc-profile-content {
                min-height: 120px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .disc-loading {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 1rem;
                color: #64748b;
            }

            .disc-spinner {
                width: 32px;
                height: 32px;
                border: 3px solid #e2e8f0;
                border-top: 3px solid #0ea5e9;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .disc-profile-completed {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 1rem;
                background: white;
                border-radius: 6px;
                border: 1px solid #d1d5db;
            }

            .disc-badge {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.5rem;
                font-weight: bold;
                color: white;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                flex-shrink: 0;
            }

            .disc-badge.D { background: linear-gradient(135deg, #dc2626, #ef4444); }
            .disc-badge.I { background: linear-gradient(135deg, #ea580c, #f97316); }
            .disc-badge.S { background: linear-gradient(135deg, #16a34a, #22c55e); }
            .disc-badge.C { background: linear-gradient(135deg, #2563eb, #3b82f6); }

            .disc-info {
                flex: 1;
            }

            .disc-info h4 {
                margin: 0 0 0.5rem 0;
                color: #1e293b;
                font-size: 1.1rem;
                font-weight: 600;
            }

            .disc-info p {
                margin: 0;
                color: #64748b;
                font-size: 0.9rem;
                line-height: 1.4;
            }

            .disc-learn-more {
                background: #0ea5e9;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 4px;
                font-size: 0.9rem;
                cursor: pointer;
                transition: background-color 0.2s;
                flex-shrink: 0;
            }

            .disc-learn-more:hover {
                background: #0284c7;
            }

            .disc-pending {
                text-align: center;
                color: #64748b;
                font-style: italic;
            }

            /***** DISC DETAIL MODAL *****/
            .disc-detail-content {
                max-width: 600px;
                max-height: 80vh;
            }

            .disc-detail-header {
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-bottom: 2rem;
                padding-bottom: 1rem;
                border-bottom: 1px solid #e5e7eb;
            }

            .disc-description {
                margin: 0;
                color: #374151;
                line-height: 1.6;
                flex: 1;
            }

            .disc-detail-sections {
                display: flex;
                flex-direction: column;
                gap: 1.5rem;
            }

            .disc-detail-section h3 {
                margin: 0 0 0.75rem 0;
                color: #1f2937;
                font-size: 1.1rem;
                font-weight: 600;
            }

            .disc-detail-section ul {
                margin: 0;
                padding-left: 1.25rem;
                list-style-type: disc;
            }

            .disc-detail-section li {
                margin-bottom: 0.5rem;
                color: #374151;
                line-height: 1.5;
            }

            .disc-detail-section p {
                margin: 0;
                color: #374151;
                line-height: 1.6;
            }

            /* Unified DISC Details Styles - Seamless Integration */
            .disc-detailed-content {
                margin-top: 1.5rem;
                padding-top: 1.5rem;
                border-top: 1px solid #e5e7eb;
                animation: fadeIn 0.3s ease-in-out;
            }

            .disc-analysis-intro {
                margin-bottom: 1.5rem;
                padding: 1rem;
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                border-radius: 6px;
                border: 1px solid #e2e8f0;
                text-align: center;
            }

            .disc-analysis-intro h5 {
                margin: 0 0 0.5rem 0;
                color: #1e293b;
                font-size: 1rem;
                font-weight: 600;
            }

            .disc-analysis-intro p {
                margin: 0;
                color: #64748b;
                font-size: 0.875rem;
            }

            .disc-section {
                margin-bottom: 1.5rem;
                padding-bottom: 1.5rem;
                border-bottom: 1px solid #f1f5f9;
            }

            .disc-section:last-child {
                margin-bottom: 0;
                border-bottom: none;
                padding-bottom: 0;
            }

            .disc-section-header {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-bottom: 0.75rem;
            }

            .disc-section-icon {
                width: 20px;
                height: 20px;
                border-radius: 4px;
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 0.7rem;
                flex-shrink: 0;
            }

            .disc-section-title {
                color: #1e293b;
                font-size: 1rem;
                font-weight: 600;
                margin: 0;
                letter-spacing: 0.025em;
            }

            .disc-section-content {
                color: #475569;
                line-height: 1.6;
                font-size: 0.9rem;
                margin-left: 2.25rem;
            }

            .disc-list {
                margin: 0;
                padding-left: 0;
                list-style: none;
            }

            .disc-list li {
                position: relative;
                padding-left: 1.25rem;
                margin-bottom: 0.75rem;
                line-height: 1.5;
            }

            .disc-list li:before {
                content: '•';
                position: absolute;
                left: 0;
                color: #3b82f6;
                font-weight: 600;
                font-size: 1.1em;
            }

            .disc-list li strong {
                color: #1e293b;
                font-weight: 600;
            }

            .disc-section-content p {
                margin: 0 0 0.75rem 0;
            }

            .disc-section-content p:last-child {
                margin-bottom: 0;
            }

            .disc-section-content strong {
                color: #1e293b;
                font-weight: 600;
            }

            .disc-section-content em {
                font-style: italic;
                color: #64748b;
            }

            .disc-section-content ul {
                margin: 0.5rem 0;
                padding-left: 0;
                list-style: none;
            }

            .disc-section-content li {
                position: relative;
                padding-left: 1.25rem;
                margin-bottom: 0.4rem;
                line-height: 1.5;
            }

            .disc-section-content li:before {
                content: "•";
                position: absolute;
                left: 0;
                color: #3b82f6;
                font-weight: 600;
            }

            .disc-scores {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 0.75rem;
                margin-top: 1rem;
                padding: 1rem;
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                border-radius: 8px;
                border: 1px solid #e2e8f0;
            }

            .score-item {
                background: white;
                padding: 0.75rem;
                border-radius: 6px;
                font-size: 0.875rem;
                font-weight: 600;
                color: #1e293b;
                border: 1px solid #e2e8f0;
                text-align: center;
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                transition: all 0.2s ease-in-out;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.25rem;
            }

            .score-item:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.08);
            }

            .score-item .score-label {
                font-size: 0.75rem;
                color: #64748b;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .score-item .score-value {
                font-size: 1.1rem;
                font-weight: 700;
                color: #3b82f6;
            }

            .disc-learn-more {
                transition: all 0.3s ease-in-out;
                position: relative;
                overflow: hidden;
            }

            .disc-learn-more.expanded {
                background: linear-gradient(135deg, #64748b 0%, #475569 100%);
                transform: scale(0.98);
            }

            .disc-learn-more.expanded:hover {
                background: linear-gradient(135deg, #475569 0%, #334155 100%);
                transform: scale(1);
            }

            .disc-learn-more:before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s;
            }

            .disc-learn-more:hover:before {
                left: 100%;
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .disc-section-content {
                    margin-left: 1.5rem;
                    font-size: 0.85rem;
                }

                .disc-section-header {
                    gap: 0.5rem;
                }

                .disc-section-icon {
                    width: 18px;
                    height: 18px;
                    font-size: 0.65rem;
                }

                .disc-section-title {
                    font-size: 0.9rem;
                }

                .disc-scores {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 0.5rem;
                    padding: 0.75rem;
                }

                .score-item {
                    padding: 0.5rem;
                }

                .disc-analysis-intro {
                    padding: 0.75rem;
                }

                .disc-analysis-intro h5 {
                    font-size: 0.9rem;
                }

                .disc-analysis-intro p {
                    font-size: 0.8rem;
                }
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(-15px) scale(0.98);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
        `;
        document.head.appendChild(styleSheet);
    }

    async function loadDiscProfile(email, userCompany) {
        const discContent = document.getElementById('disc-profile-content');
        if (!discContent) {
            console.error('DISC content element not found');
            return;
        }

        console.log('Loading DISC profile for:', { email, userCompany });

        try {
            // Check if user has a DISC profile
            const db = firebase.firestore();
            const userRef = db.collection('companies').doc(userCompany).collection('users').doc(email);
            const userDoc = await userRef.get();

            if (userDoc.exists) {
                const userData = userDoc.data();
                const discProfile = userData.discProfile;

                console.log('User document found, DISC profile:', discProfile);

                if (discProfile && discProfile.status === 'completed') {
                    console.log('Displaying completed DISC profile:', discProfile.primaryType);
                    // Display completed DISC profile
                    displayDiscProfile(discProfile);
                } else if (discProfile && discProfile.status === 'processing') {
                    console.log('DISC profile is still processing');
                    // Show processing state
                    discContent.innerHTML = `
                        <div class="disc-loading">
                            <div class="disc-spinner"></div>
                            <p>Analyzing behavioral assessment...</p>
                            <button onclick="window.refreshDiscProfile && window.refreshDiscProfile()" style="margin-top: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer;">Refresh</button>
                        </div>
                    `;
                } else {
                    console.log('No DISC profile found or incomplete status:', discProfile?.status);
                    // No DISC assessment taken
                    discContent.innerHTML = `
                        <div class="disc-pending">
                            <p>DISC behavioral assessment not completed</p>
                        </div>
                    `;
                }
            } else {
                console.log('User document does not exist');
                discContent.innerHTML = `
                    <div class="disc-pending">
                        <p>User profile not found</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading DISC profile:', error);
            discContent.innerHTML = `
                <div class="disc-pending">
                    <p>Error loading DISC profile: ${error.message}</p>
                </div>
            `;
        }
    }

    function displayDiscProfile(discProfile) {
        const discContent = document.getElementById('disc-profile-content');
        if (!discContent) {
            console.error('DISC content element not found in displayDiscProfile');
            return;
        }

        const primaryType = discProfile.primaryType;
        console.log('Displaying DISC profile for primary type:', primaryType);

        const typeDescriptions = {
            'D': 'Dominance - Direct, decisive, and results-oriented',
            'I': 'Influence - Inspiring, enthusiastic, and people-focused',
            'S': 'Steadiness - Supportive, reliable, and team-oriented',
            'C': 'Conscientiousness - Careful, analytical, and quality-focused'
        };

        if (!typeDescriptions[primaryType]) {
            console.error('Invalid primary type:', primaryType);
            discContent.innerHTML = `
                <div class="disc-pending">
                    <p>Invalid DISC profile data</p>
                </div>
            `;
            return;
        }

        // Store the full profile data for the expand/collapse functionality
        discContent.dataset.discProfile = JSON.stringify(discProfile);

        const fullTypeName = primaryType === 'D' ? 'Dominance' :
                            primaryType === 'I' ? 'Influence' :
                            primaryType === 'S' ? 'Steadiness' : 'Conscientiousness';

        discContent.innerHTML = `
            <div class="disc-profile-completed">
                <div class="disc-badge ${primaryType}">${primaryType}</div>
                <div class="disc-info">
                    <h4>Primary Type: ${primaryType} - ${fullTypeName}</h4>
                    <p>${typeDescriptions[primaryType]}</p>
                    <div class="disc-scores">
                        <div class="score-item">
                            <span class="score-label">Dominance</span>
                            <span class="score-value">${discProfile.scores?.D || 0}%</span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">Influence</span>
                            <span class="score-value">${discProfile.scores?.I || 0}%</span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">Steadiness</span>
                            <span class="score-value">${discProfile.scores?.S || 0}%</span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">Conscientiousness</span>
                            <span class="score-value">${discProfile.scores?.C || 0}%</span>
                        </div>
                    </div>
                    <button class="disc-learn-more" onclick="toggleDiscDetails()">Learn More</button>
                    <div id="disc-detailed-content" class="disc-detailed-content" style="display: none;">
                        ${formatDiscReport(discProfile, primaryType, fullTypeName)}
                    </div>
                </div>
            </div>
        `;

        console.log('DISC profile display completed successfully');
    }

    function formatDiscReport(discProfile, primaryType, fullTypeName) {
        // Check if we have structured analysis (new format) or text report (legacy format)
        const hasStructuredAnalysis = discProfile && discProfile.detailedAnalysis;
        const hasTextReport = discProfile && discProfile.detailedReport;

        if (!hasStructuredAnalysis && !hasTextReport) {
            return '<div class="disc-analysis-intro"><h5>Detailed Analysis</h5><p>No detailed analysis available</p></div>';
        }

        console.log('Formatting DISC report:', {
            hasStructuredAnalysis,
            hasTextReport,
            primaryType,
            fullTypeName
        });

        // Start with intro section
        let formattedReport = `
            <div class="disc-analysis-intro">
                <h5>Detailed Behavioral Analysis</h5>
                <p>Comprehensive insights into your ${fullTypeName} personality profile</p>
            </div>
        `;

        if (hasStructuredAnalysis) {
            console.log('Using structured analysis format');
            return formatStructuredAnalysis(discProfile.detailedAnalysis, formattedReport);
        } else {
            console.log('Using legacy text format');
            // Define section icons for legacy parsing
            const sectionIcons = {
                'OVERVIEW': '👤', 'STRENGTHS': '💪', 'POTENTIAL CHALLENGES': '⚠️', 'CHALLENGES': '⚠️',
                'COMMUNICATION STYLE': '💬', 'COMMUNICATION': '💬', 'WORK ENVIRONMENT': '🏢', 'WORK': '🏢',
                'LEADERSHIP STYLE': '👑', 'LEADERSHIP': '👑', 'TEAM DYNAMICS': '🤝', 'TEAM': '🤝',
                'PROFESSIONAL DEVELOPMENT': '📈', 'DEVELOPMENT': '📈'
            };
            return parseAlternativeFormat(discProfile.detailedReport, sectionIcons, formattedReport);
        }
    }

    function formatStructuredAnalysis(analysis, formattedReport) {
        console.log('Formatting structured DISC analysis:', analysis);

        // Define section configurations
        const sections = [
            {
                key: 'overview',
                title: 'Overview',
                icon: '👤',
                type: 'text'
            },
            {
                key: 'strengths',
                title: 'Strengths',
                icon: '💪',
                type: 'list'
            },
            {
                key: 'challenges',
                title: 'Potential Challenges',
                icon: '⚠️',
                type: 'list'
            },
            {
                key: 'communicationStyle',
                title: 'Communication Style',
                icon: '💬',
                type: 'text'
            },
            {
                key: 'workEnvironment',
                title: 'Work Environment',
                icon: '🏢',
                type: 'text'
            },
            {
                key: 'leadershipStyle',
                title: 'Leadership Style',
                icon: '👑',
                type: 'text'
            },
            {
                key: 'teamDynamics',
                title: 'Team Dynamics',
                icon: '🤝',
                type: 'text'
            },
            {
                key: 'professionalDevelopment',
                title: 'Professional Development',
                icon: '📈',
                type: 'list'
            }
        ];

        // Process each section
        sections.forEach(section => {
            const data = analysis[section.key];
            if (!data) {
                console.log(`No data found for section: ${section.key}`);
                return;
            }

            formattedReport += `
                <div class="disc-section">
                    <div class="disc-section-header">
                        <div class="disc-section-icon">${section.icon}</div>
                        <h4 class="disc-section-title">${section.title}</h4>
                    </div>
                    <div class="disc-section-content">
                        ${formatSectionData(data, section.type)}
                    </div>
                </div>
            `;
        });

        console.log('Structured analysis formatting completed');
        return formattedReport;
    }

    function formatSectionData(data, type) {
        if (type === 'text') {
            return `<p>${data}</p>`;
        } else if (type === 'list' && Array.isArray(data)) {
            if (data.length === 0) return '<p>No items available</p>';

            return `
                <ul class="disc-list">
                    ${data.map(item => {
                        if (typeof item === 'string') {
                            return `<li>${item}</li>`;
                        } else if (item.title && item.description) {
                            return `<li><strong>${item.title}:</strong> ${item.description}</li>`;
                        } else if (item.area && item.recommendation) {
                            return `<li><strong>${item.area}:</strong> ${item.recommendation}</li>`;
                        } else {
                            return `<li>${JSON.stringify(item)}</li>`;
                        }
                    }).join('')}
                </ul>
            `;
        } else {
            console.warn('Unknown section type or invalid data:', type, data);
            return `<p>${typeof data === 'string' ? data : JSON.stringify(data)}</p>`;
        }
    }

    function parseAlternativeFormat(detailedReport, sectionIcons, formattedReport) {
        console.log('Using alternative parsing format');
        console.log('Total lines to parse:', detailedReport.split('\n').length);

        // Look for section headers that appear as standalone lines or with decorative elements
        const lines = detailedReport.split('\n');
        let currentSection = null;
        let currentContent = [];
        let sectionsFound = 0;

        // Skip initial content until we find the first section header
        let foundFirstSection = false;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Skip empty lines
            if (line.length === 0) continue;

            // Check if this line contains a section header
            // Look for lines that contain section names, especially with decorative elements
            let sectionMatch = null;

            // Primary pattern: lines with dashes and section names
            if (line.match(/[-─═_]{10,}/)) {
                sectionMatch = line.match(/(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT)/i);
            }

            // Fallback: standalone section names
            if (!sectionMatch) {
                sectionMatch = line.match(/^(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT)\s*$/i);
            }

            if (sectionMatch) {
                foundFirstSection = true;
                console.log(`Found section header: "${sectionMatch[1]}" at line ${i + 1}: "${line}"`);

                // Save previous section if exists
                if (currentSection && currentContent.length > 0) {
                    const cleanTitle = currentSection.toUpperCase();
                    const displayTitle = cleanTitle.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                    const icon = sectionIcons[cleanTitle] || sectionIcons[Object.keys(sectionIcons).find(key => cleanTitle.includes(key))] || '📋';

                    console.log(`Saving section "${cleanTitle}" with ${currentContent.length} lines of content`);
                    console.log(`Content preview: "${currentContent.join(' ').substring(0, 100)}..."`);

                    formattedReport += `
                        <div class="disc-section">
                            <div class="disc-section-header">
                                <div class="disc-section-icon">${icon}</div>
                                <h4 class="disc-section-title">${displayTitle}</h4>
                            </div>
                            <div class="disc-section-content">${formatSectionContent(currentContent.join('\n').trim())}</div>
                        </div>
                    `;
                    sectionsFound++;
                }

                // Start new section
                currentSection = sectionMatch[1];
                currentContent = [];
            } else if (foundFirstSection && currentSection) {
                // Add content to current section (only after we've found the first section)
                currentContent.push(line);
            } else if (!foundFirstSection && !currentSection) {
                // Content before any section header - treat as overview
                console.log('Found content before any section header, treating as OVERVIEW');
                currentSection = 'OVERVIEW';
                currentContent = [line];
                foundFirstSection = true;
            }
        }

        // Save the last section
        if (currentSection && currentContent.length > 0) {
            const cleanTitle = currentSection.toUpperCase();
            const displayTitle = cleanTitle.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
            const icon = sectionIcons[cleanTitle] || sectionIcons[Object.keys(sectionIcons).find(key => cleanTitle.includes(key))] || '📋';

            console.log(`Saving final section "${cleanTitle}" with ${currentContent.length} lines of content`);

            formattedReport += `
                <div class="disc-section">
                    <div class="disc-section-header">
                        <div class="disc-section-icon">${icon}</div>
                        <h4 class="disc-section-title">${displayTitle}</h4>
                    </div>
                    <div class="disc-section-content">${formatSectionContent(currentContent.join('\n').trim())}</div>
                </div>
            `;
            sectionsFound++;
        }

        console.log(`Alternative parsing completed. Total sections found: ${sectionsFound}`);
        return formattedReport;
    }

    function formatSectionContent(content) {
        if (!content) {
            console.log('formatSectionContent: No content provided');
            return '';
        }

        console.log('formatSectionContent: Processing content length:', content.length);
        console.log('formatSectionContent: Content preview:', content.substring(0, 200));

        // Split content into paragraphs
        const paragraphs = content.split(/\n\s*\n/);
        let formattedContent = '';

        console.log('formatSectionContent: Split into', paragraphs.length, 'paragraphs');

        paragraphs.forEach((paragraph, index) => {
            const trimmed = paragraph.trim();
            if (!trimmed) return;

            console.log(`formatSectionContent: Processing paragraph ${index + 1}:`, trimmed.substring(0, 100));

            // Check if this paragraph contains bullet points
            const lines = trimmed.split('\n');
            const hasBullets = lines.some(line => line.trim().match(/^[-•*]\s+/));

            if (hasBullets) {
                console.log(`formatSectionContent: Paragraph ${index + 1} has bullets`);
                // Format as a list
                formattedContent += '<ul>';
                lines.forEach(line => {
                    const trimmedLine = line.trim();
                    if (trimmedLine.match(/^[-•*]\s+/)) {
                        const listItem = trimmedLine.replace(/^[-•*]\s+/, '');
                        formattedContent += `<li>${formatInlineText(listItem)}</li>`;
                    } else if (trimmedLine) {
                        // Non-bullet line in a bullet context, treat as continuation
                        formattedContent += `<li>${formatInlineText(trimmedLine)}</li>`;
                    }
                });
                formattedContent += '</ul>';
            } else {
                console.log(`formatSectionContent: Paragraph ${index + 1} is regular text`);
                // Format as regular paragraph
                const formattedParagraph = lines
                    .map(line => line.trim())
                    .filter(line => line)
                    .join(' ');

                if (formattedParagraph) {
                    formattedContent += `<p>${formatInlineText(formattedParagraph)}</p>`;
                }
            }
        });

        console.log('formatSectionContent: Final formatted content length:', formattedContent.length);
        return formattedContent;
    }

    function formatInlineText(text) {
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\b(DISC|D|I|S|C)\b/g, '<strong>$1</strong>');
    }

    // Global function for toggling DISC details
    window.toggleDiscDetails = function() {
        const detailsContent = document.getElementById('disc-detailed-content');
        const button = document.querySelector('.disc-learn-more');

        if (!detailsContent || !button) {
            console.error('DISC details elements not found', {
                detailsContent: !!detailsContent,
                button: !!button
            });
            return;
        }

        const isExpanded = detailsContent.style.display !== 'none';
        console.log('Toggling DISC details, currently expanded:', isExpanded);

        if (isExpanded) {
            // Collapse
            detailsContent.style.display = 'none';
            button.textContent = 'Learn More';
            button.classList.remove('expanded');
            console.log('DISC details collapsed');
        } else {
            // Expand
            detailsContent.style.display = 'block';
            button.textContent = 'Show Less';
            button.classList.add('expanded');
            console.log('DISC details expanded');

            // Smooth scroll to show the expanded content
            setTimeout(() => {
                detailsContent.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }, 100);
        }
    };

    let discPollingInterval = null;

    // Global refresh function for manual testing
    window.refreshDiscProfile = async function() {
        if (currentData && currentData.report && currentData.report.employeeEmail) {
            const userCompany = currentData.report.userCompany || 'Birmingham';
            console.log('Manual DISC profile refresh triggered');
            await loadDiscProfile(currentData.report.employeeEmail, userCompany);
        }
    };

    function startDiscProfilePolling(email, userCompany) {
        // Clear any existing polling
        if (discPollingInterval) {
            clearInterval(discPollingInterval);
        }

        // Check if DISC profile is still processing
        const discContent = document.getElementById('disc-profile-content');
        if (!discContent) return;

        const isProcessing = discContent.innerHTML.includes('Processing behavioral assessment') ||
                           discContent.innerHTML.includes('Analyzing behavioral assessment');

        if (isProcessing) {
            console.log('Starting DISC profile polling...');

            discPollingInterval = setInterval(async () => {
                console.log('Polling for DISC profile updates...');
                await loadDiscProfile(email, userCompany);

                // Stop polling if profile is now completed or failed
                const updatedContent = document.getElementById('disc-profile-content');
                if (updatedContent && !updatedContent.innerHTML.includes('disc-spinner')) {
                    console.log('DISC profile polling completed');
                    clearInterval(discPollingInterval);
                    discPollingInterval = null;
                }
            }, 3000); // Poll every 3 seconds

            // Stop polling after 2 minutes to prevent infinite polling
            setTimeout(() => {
                if (discPollingInterval) {
                    console.log('DISC profile polling timeout reached');
                    clearInterval(discPollingInterval);
                    discPollingInterval = null;
                }
            }, 120000); // 2 minutes
        }
    }

    function initializeEventListeners(overlay) {
        // Clean up old listeners if any
        const existingOverlay = document.getElementById('skills-gap-overlay');
        if (existingOverlay) {
            const oldCloseButton = existingOverlay.querySelector('#close-skills-modal');
            if (oldCloseButton) {
                oldCloseButton.removeEventListener('click', hideModal);
            }
            existingOverlay.removeEventListener('click', overlayClickHandler);
        }

        // Close button
        const closeButton = overlay.querySelector('#close-skills-modal');
        if (closeButton) {
            closeButton.addEventListener('click', hideModal);
        }

        // Overlay click to close
        overlay.addEventListener('click', overlayClickHandler);

        // Collapsible cross-path recommendations
        const toggleButton = overlay.querySelector('#toggle-cross-paths-btn');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                const collapsible = overlay.querySelector('#cross-paths-collapsible');
                if (collapsible) {
                    collapsible.classList.toggle('expanded');
                }
            });
        }
    }

    function overlayClickHandler(event) {
        // Close modal if user clicks outside content
        if (event.target.id === 'skills-gap-overlay') {
            hideModal();
        }
    }

    async function hideModal() {
        isClosing = true;
        const overlay = document.getElementById('skills-gap-overlay');
        if (!overlay) return;

        overlay.style.opacity = '0';
        const modalContent = overlay.querySelector('.modal-content');
        if (modalContent) {
            modalContent.style.opacity = '0';
            modalContent.style.transform = 'scale(0.95)';
        }

        // Clean up chart instance
        if (currentChartInstance) {
            currentChartInstance.destroy();
            currentChartInstance = null;
        }

        // Clean up DISC polling
        if (discPollingInterval) {
            clearInterval(discPollingInterval);
            discPollingInterval = null;
        }

        await new Promise(resolve => setTimeout(resolve, 300));
        overlay.style.display = 'none';
    }

    function showModal() {
        const overlay = document.getElementById('skills-gap-overlay');
        if (overlay) {
            overlay.style.display = 'flex';
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        }
    }

    // Global function to show DISC details
    global.showDiscDetails = function(primaryType) {
        const typeDetails = {
            'D': {
                title: 'Dominance',
                description: 'People with high D styles are direct, decisive, and results-oriented. They prefer to take charge and make quick decisions.',
                strengths: ['Results-oriented', 'Direct communication', 'Quick decision-making', 'Competitive', 'Independent'],
                challenges: ['May appear impatient', 'Can be overly direct', 'May overlook details', 'Might not consider others\' feelings'],
                workStyle: 'Prefers challenging assignments, authority, and varied activities. Works best with minimal supervision and clear goals.'
            },
            'I': {
                title: 'Influence',
                description: 'People with high I styles are inspiring, enthusiastic, and people-focused. They excel at motivating others and building relationships.',
                strengths: ['Enthusiastic', 'Persuasive', 'Optimistic', 'Collaborative', 'Creative'],
                challenges: ['May be overly optimistic', 'Can be disorganized', 'May talk too much', 'Might avoid conflict'],
                workStyle: 'Thrives in team environments, enjoys recognition, and prefers variety. Works best with people interaction and positive feedback.'
            },
            'S': {
                title: 'Steadiness',
                description: 'People with high S styles are supportive, reliable, and team-oriented. They value stability and prefer to work in harmony.',
                strengths: ['Reliable', 'Patient', 'Good listener', 'Team player', 'Consistent'],
                challenges: ['May resist change', 'Can be overly accommodating', 'May avoid confrontation', 'Might be slow to act'],
                workStyle: 'Prefers stable environments, clear expectations, and time to adjust to change. Works best with supportive teams and defined processes.'
            },
            'C': {
                title: 'Conscientiousness',
                description: 'People with high C styles are careful, analytical, and quality-focused. They value accuracy and systematic approaches.',
                strengths: ['Detail-oriented', 'Analytical', 'Quality-focused', 'Systematic', 'Diplomatic'],
                challenges: ['May be overly critical', 'Can be perfectionist', 'May be slow to decide', 'Might avoid risks'],
                workStyle: 'Prefers clear standards, time to analyze, and quality over speed. Works best with detailed information and minimal interruptions.'
            }
        };

        const details = typeDetails[primaryType];
        if (!details) return;

        // Create DISC detail modal
        const discModal = document.createElement('div');
        discModal.id = 'disc-detail-modal';
        discModal.className = 'modal-overlay';
        discModal.innerHTML = `
            <div class="modal-content disc-detail-content">
                <div class="modal-header">
                    <div class="modal-title-container">
                        <h2 class="modal-title">DISC Type: ${details.title}</h2>
                    </div>
                    <div class="modal-actions">
                        <button id="close-disc-modal" class="close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="disc-detail-header">
                        <div class="disc-badge ${primaryType}">${primaryType}</div>
                        <p class="disc-description">${details.description}</p>
                    </div>

                    <div class="disc-detail-sections">
                        <div class="disc-detail-section">
                            <h3>Strengths</h3>
                            <ul>
                                ${details.strengths.map(strength => `<li>${strength}</li>`).join('')}
                            </ul>
                        </div>

                        <div class="disc-detail-section">
                            <h3>Potential Challenges</h3>
                            <ul>
                                ${details.challenges.map(challenge => `<li>${challenge}</li>`).join('')}
                            </ul>
                        </div>

                        <div class="disc-detail-section">
                            <h3>Work Style</h3>
                            <p>${details.workStyle}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(discModal);

        // Add event listeners
        const closeButton = discModal.querySelector('#close-disc-modal');
        closeButton.addEventListener('click', () => {
            discModal.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(discModal);
            }, 300);
        });

        discModal.addEventListener('click', (e) => {
            if (e.target === discModal) {
                discModal.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(discModal);
                }, 300);
            }
        });

        // Animate appearance
        setTimeout(() => {
            discModal.style.opacity = '1';
        }, 10);
    };

    // Public API
    global.showSkillsGapAnalysis = showSkillsGapAnalysis;

})(typeof window !== 'undefined' ? window : global);
